<view class="promotion-code-modal" wx:if="{{show}}" bindtap="onMaskTap">
  <view class="modal-container" bindtap="onContentTap">
    <view class="modal-header">
      <text class="modal-title">填写推广码</text>
    </view>
    
    <view class="modal-content">
      <view class="promotion-tip">请输入员工推广码建立推广关系</view>
      
      <view class="promotion-input-wrapper">
        <input 
          class="promotion-input" 
          value="{{promotionCode}}" 
          placeholder="请输入推广码" 
          maxlength="20" 
          bindinput="onPromotionCodeInput"
          focus="{{show}}"
        />
        
        <view wx:if="{{isCheckingCode}}" class="checking-status">验证中...</view>
        <view wx:elif="{{promotionCodeInfo && promotionCode}}" class="code-status">
          <text wx:if="{{promotionCodeInfo.available}}" class="success">
            ✓ {{promotionCodeInfo.employee.name}}
          </text>
          <text wx:else class="error">
            ✗ {{promotionCodeInfo.message}}
          </text>
        </view>
      </view>
      
      <view class="promotion-notice">
        <text class="notice-text">注意：每个用户只能填写一次推广码，建立后不可更改</text>
      </view>
    </view>
    
    <view class="modal-buttons">
      <view class="modal-btn cancel" bindtap="onCancel">取消</view>
      <view class="modal-btn confirm" bindtap="onConfirm">确定</view>
    </view>
  </view>
</view>
