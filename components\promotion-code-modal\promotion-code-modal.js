Component({
  properties: {
    show: {
      type: Boolean,
      value: false
    },
    isCheckingCode: {
      type: Boolean,
      value: false
    },
    promotionCodeInfo: {
      type: Object,
      value: null
    }
  },

  data: {
    promotionCode: ''
  },

  methods: {
    // 推广码输入
    onPromotionCodeInput(e) {
      const code = e.detail.value.trim().toUpperCase();
      this.setData({
        promotionCode: code
      });

      // 触发输入事件
      this.triggerEvent('input', { code });
    },

    // 确定按钮
    onConfirm() {
      const { promotionCode } = this.data;
      const { promotionCodeInfo } = this.properties;

      if (!promotionCode) {
        wx.showToast({
          title: '请输入推广码',
          icon: 'none'
        });
        return;
      }

      if (!promotionCodeInfo || !promotionCodeInfo.available) {
        wx.showToast({
          title: '推广码无效，请重新输入',
          icon: 'none'
        });
        return;
      }

      this.triggerEvent('confirm', { promotionCode });
    },

    // 取消按钮
    onCancel() {
      this.setData({
        promotionCode: ''
      });
      this.triggerEvent('cancel');
    },

    // 点击遮罩层关闭
    onMaskTap() {
      this.onCancel();
    },

    // 阻止事件冒泡
    onContentTap() {
      // 阻止事件冒泡到遮罩层
    }
  }
});
